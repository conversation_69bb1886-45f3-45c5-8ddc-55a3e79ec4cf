"""
Simplified GraspLDM Inference Interface

This module provides a streamlined inference interface for GraspLDM that bypasses
the complex dataset loading pipeline and accepts raw point cloud data directly.

Key Features:
- Direct point cloud input (numpy arrays or torch tensors)
- Camera extrinsic parameter support
- Same preprocessing pipeline as original implementation
- Compatible with existing model weights and configurations
- Suitable for real-time robotic applications

Usage Example:
    Basic inference:
    >>> inference = SimpleGraspLDMInference(exp_path="path/to/experiment")
    >>> results = inference.infer_from_pointcloud(pointcloud_array, num_grasps=20)
    
    With visualization and saving:
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     visualize=True,
    ...     save_path="grasps_visualization.glb"  # Supports .glb, .ply, .obj, .stl
    ... )

    # Generate grasps from point cloud
    results = inference.infer_from_pointcloud(
        pointcloud=pc_array,  # [N, 3] numpy array or torch tensor
        camera_pose=cam_pose, # [4, 4] camera extrinsic matrix (optional)
        num_grasps=20,
        visualize=True
    )
"""

import os
import warnings
from typing import Union, Optional, Tuple, Dict, Any

import numpy as np
import torch
import torch.nn as nn

from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from grasp_ldm.utils.rotations import tmrp_to_H
from grasp_ldm.utils.pointcloud_helpers import PointCloudHelpers
from grasp_ldm.utils.vis import visualize_pc_grasps
from tools.inference import Experiment, fix_state_dict_prefix, unnormalize_grasps, unnormalize_pc


class SimpleGraspLDMInference:
    """
    Simplified GraspLDM inference interface for direct point cloud input.

    This class provides a streamlined interface that bypasses the complex ACRONYM
    dataset structure and accepts raw point cloud data directly from cameras or
    other sources. It maintains full compatibility with existing model weights
    while offering a more flexible API for real-time applications.

    Key Capabilities:
    - Direct point cloud input (no dataset required)
    - Camera pose transformation support
    - Automatic point cloud preprocessing (centering, normalization)
    - Multiple conditioning modes (unconditional, class, region)
    - Built-in visualization support
    - Same output format as original inference pipeline

    Dimension Flow:
    - Input PC: [N, 3] raw coordinates → [1024, 3] regularized
    - Preprocessing: centering + normalization → [1024, 3] standardized
    - LDM Inference: [1024, 3] → [num_grasps, 4, 4] transformation matrices
    """

    def __init__(
        self,
        exp_path: str,                          # Path to experiment directory
        device: str = "cuda:0",                 # Compute device
        use_ema_model: bool = True,             # Use EMA model weights
        use_fast_sampler: bool = True,          # Enable fast sampling (DDIM)
        num_inference_steps: Optional[int] = 100, # Number of denoising steps
        num_points: int = 1024,                 # Target point cloud size
    ):
        """
        Initialize the simplified inference interface.

        Args:
            exp_path: Path to experiment directory containing model checkpoints
            device: PyTorch device for computation
            use_ema_model: Whether to use Exponential Moving Average weights
            use_fast_sampler: Enable fast sampling with DDIM scheduler
            num_inference_steps: Number of denoising steps for diffusion
            num_points: Target number of points for point cloud regularization
        """
        self.device = torch.device(device)
        self.use_ema_model = use_ema_model
        self.num_points = num_points

        # Initialize experiment handler
        self.experiment = Experiment(
            exp_name=os.path.basename(exp_path),
            exp_out_root=os.path.dirname(exp_path),
            modes=["ddm", "vae"]
        )

        # Load configuration
        self.config = self.experiment.get_config("ddm")

        # Setup diffusion sampler
        self._setup_ldm_sampler(num_inference_steps, use_fast_sampler)

        # Load model
        self.model = self._load_model()

        # Set normalization parameters (from dataset statistics)
        self._set_normalization_params()

        # Sigmoid for confidence scores
        self._sigmoid = nn.Sigmoid()

        print(f"✓ SimpleGraspLDMInference initialized")
        print(f"  Device: {self.device}")
        print(f"  Model: {'EMA' if use_ema_model else 'Standard'}")
        print(f"  Sampler: {'Fast DDIM' if use_fast_sampler else 'Standard DDPM'}")
        print(f"  Inference steps: {self.num_inference_steps}")

    def _setup_ldm_sampler(self, num_inference_steps: Optional[int], use_fast_sampler: bool):
        """Configure the diffusion sampler parameters."""
        if use_fast_sampler:
            self.config.models.ddm.model.args.noise_scheduler_type = "ddim"
            self.fast_sampler = "DDIM"
            self.num_inference_steps = 100 if num_inference_steps is None else num_inference_steps
        else:
            self.fast_sampler = None
            self.num_inference_steps = 1000 if num_inference_steps is None else num_inference_steps

    def _load_model(self):
        """Load the trained LDM model with VAE."""
        # Build model from configuration
        model = build_model_from_cfg(self.config.model.ddm)
        model.set_vae_model(build_model_from_cfg(self.config.model.vae))

        # Load checkpoint
        ckpt_path = self.experiment.get_ckpt_path("ddm")
        state_dict = torch.load(ckpt_path, map_location=self.device)["state_dict"]

        # Use appropriate model prefix (EMA vs standard)
        model_prefix = "model" if not self.use_ema_model else "ema_model.online_model"
        state_dict = fix_state_dict_prefix(state_dict, model_prefix, ignore_all_others=True)

        # Load weights
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=True)
        if missing_keys:
            warnings.warn(f"Missing keys while loading state dict: {missing_keys}")
        if unexpected_keys:
            warnings.warn(f"Found unexpected keys while loading state dict: {unexpected_keys}")

        return model.eval().to(self.device)

    def _set_normalization_params(self):
        """
        Set normalization parameters based on dataset statistics.

        These parameters are derived from the training data and must match
        exactly for proper model performance. The values are based on the
        AcronymPartialPointclouds dataset preprocessing pipeline.
        """
        # Point cloud normalization (zero-centered after per-object centering)
        self._INPUT_PC_SHIFT = torch.zeros((3,), device=self.device)
        self._INPUT_PC_SCALE = torch.ones((3,), device=self.device) * 0.05  # translation_scale

        # Grasp normalization (zero-centered after per-object centering)
        self._INPUT_GRASP_SHIFT = torch.zeros((6,), device=self.device)
        self._INPUT_GRASP_SCALE = torch.cat([
            torch.ones((3,), device=self.device) * 0.05,  # translation_scale
            torch.ones((3,), device=self.device) * 0.5,   # rotation_scale
        ])

        print(f"✓ Normalization parameters set:")
        print(f"  PC scale: {self._INPUT_PC_SCALE[0].item():.3f}")
        print(f"  Grasp translation scale: {self._INPUT_GRASP_SCALE[0].item():.3f}")
        print(f"  Grasp rotation scale: {self._INPUT_GRASP_SCALE[3].item():.3f}")

    def _prepare_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Prepare raw point cloud for inference.

        This method performs the same preprocessing steps as the original
        AcronymPartialPointclouds dataset:
        1. Convert to torch tensor
        2. Apply camera pose transformation (if provided)
        3. Regularize point count to target size
        4. Convert to float32

        Args:
            pointcloud: Raw point cloud [N, 3] in camera or world coordinates
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation

        Returns:
            torch.Tensor: Prepared point cloud [num_points, 3]
        """
        # Convert to torch tensor
        if isinstance(pointcloud, np.ndarray):
            pc = torch.from_numpy(pointcloud).float()
        else:
            pc = pointcloud.float()

        # Ensure correct shape
        if pc.ndim != 2 or pc.shape[1] != 3:
            raise ValueError(f"Point cloud must have shape [N, 3], got {pc.shape}")

        # Apply camera pose transformation if provided
        if camera_pose is not None:
            pc = self._transform_pointcloud(pc, camera_pose)

        # Regularize point count
        pc = self._regularize_pointcloud(pc, self.num_points)

        return pc.to(self.device)

    def _transform_pointcloud(
        self,
        pointcloud: torch.Tensor,
        camera_pose: Union[np.ndarray, torch.Tensor]
    ) -> torch.Tensor:
        """
        Transform point cloud using camera extrinsic parameters.

        Args:
            pointcloud: Point cloud in camera coordinates [N, 3]
            camera_pose: Camera extrinsic matrix [4, 4] (camera to world transform)

        Returns:
            torch.Tensor: Transformed point cloud [N, 3]
        """
        if isinstance(camera_pose, np.ndarray):
            camera_pose = torch.from_numpy(camera_pose).float()

        # Ensure correct shape
        if camera_pose.shape != (4, 4):
            raise ValueError(f"Camera pose must be [4, 4] matrix, got {camera_pose.shape}")

        # Convert to homogeneous coordinates
        ones = torch.ones(pointcloud.shape[0], 1, dtype=pointcloud.dtype, device=pointcloud.device)
        pc_homogeneous = torch.cat([pointcloud, ones], dim=1)  # [N, 4]

        # Apply transformation
        pc_transformed = torch.matmul(pc_homogeneous, camera_pose.T)  # [N, 4]

        # Return to 3D coordinates
        return pc_transformed[:, :3]

    def _regularize_pointcloud(self, pointcloud: torch.Tensor, target_points: int) -> torch.Tensor:
        """
        Regularize point cloud to have exactly target_points points.

        This matches the behavior of PointCloudHelpers.regularize_pc_point_count
        used in the original dataset pipeline.

        Args:
            pointcloud: Input point cloud [N, 3]
            target_points: Target number of points

        Returns:
            torch.Tensor: Regularized point cloud [target_points, 3]
        """
        current_points = pointcloud.shape[0]

        if current_points < target_points:
            # Upsample: repeat existing points
            multiplier = max(target_points // current_points, 1)
            pc = pointcloud.repeat(multiplier, 1)

            # Add random points to reach exact target
            num_extra = target_points - pc.shape[0]
            if num_extra > 0:
                extra_indices = torch.randperm(pc.shape[0])[:num_extra]
                extra_points = pc[extra_indices]
                pc = torch.cat([pc, extra_points], dim=0)

        elif current_points > target_points:
            # Downsample: random selection
            indices = torch.randperm(current_points)[:target_points]
            pc = pointcloud[indices]
        else:
            pc = pointcloud

        return pc

    def _preprocess_pointcloud(self, pointcloud: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Apply the same preprocessing pipeline as AcronymPartialPointclouds.

        This includes:
        1. Centering on point cloud mean
        2. Normalization using dataset statistics
        3. Metadata preparation for unnormalization

        Args:
            pointcloud: Prepared point cloud [num_points, 3]

        Returns:
            tuple: (preprocessed_pc, metadata_dict)
        """
        # Step 1: Center on point cloud mean
        pc_mean = torch.mean(pointcloud, dim=0)  # [3]
        pc_centered = pointcloud - pc_mean

        # Step 2: Normalize using dataset statistics
        pc_normalized = (pc_centered - self._INPUT_PC_SHIFT) / self._INPUT_PC_SCALE

        # Step 3: Prepare metadata for unnormalization
        grasp_mean = self._INPUT_GRASP_SHIFT.clone()
        grasp_mean[:3] += pc_mean  # Add centering offset to translation components

        metas = {
            "pc_mean": self._INPUT_PC_SHIFT + pc_mean,
            "pc_std": self._INPUT_PC_SCALE,
            "grasp_mean": grasp_mean,
            "grasp_std": self._INPUT_GRASP_SCALE,
            "dataset_normalized": True,
        }

        return pc_normalized, metas

    def _generate_grasps(
        self,
        pointcloud: torch.Tensor,
        metas: Dict[str, torch.Tensor],
        num_grasps: int = 20,
        return_intermediate: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Generate grasps using the LDM model.

        This method implements the core LDM inference pipeline:
        1. Point cloud encoding to latent space
        2. Diffusion sampling in grasp latent space
        3. VAE decoding to grasp poses
        4. Post-processing and unnormalization

        Args:
            pointcloud: Preprocessed point cloud [num_points, 3]
            metas: Metadata dictionary for unnormalization
            num_grasps: Number of grasps to generate
            return_intermediate: Whether to return intermediate diffusion steps

        Returns:
            dict: Generated results containing:
                - grasps: [num_grasps, 4, 4] homogeneous transformation matrices
                - confidence: [num_grasps, 1] success probabilities
                - pc: [num_points, 3] unnormalized point cloud
                - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
        """
        # Ensure batch dimension
        batch_pc = pointcloud.unsqueeze(0)  # [1, num_points, 3]

        # Move metadata to device
        metas = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in metas.items()}

        # Prepare model input
        in_kwargs = {"xyz": batch_pc, "metas": metas}

        # Configure fast sampling if enabled
        if self.fast_sampler == "DDIM":
            self.model.set_inference_timesteps(self.num_inference_steps)

        # Execute LDM inference with intermediate step control
        with torch.no_grad():
            final_grasps, all_diffusion_grasps = self.model.generate_grasps(
                num_grasps=num_grasps, 
                return_intermediate=return_intermediate, 
                **in_kwargs
            )

        # Parse model outputs
        if self.model.vae_model.decoder._use_qualities:
            tmrp, cls_logit, qualities = final_grasps
        else:
            tmrp, cls_logit = final_grasps
            qualities = None

        # Reshape to proper dimensions
        tmrp = tmrp.view(1, num_grasps, tmrp.shape[-1])

        # Unnormalize grasp poses
        grasp_unnorm = unnormalize_grasps(tmrp, metas)

        # Convert to homogeneous transformation matrices
        H_grasps = tmrp_to_H(grasp_unnorm)  # [1, num_grasps, 4, 4]

        # Process intermediate diffusion steps if available
        all_steps_grasps = []
        if return_intermediate and all_diffusion_grasps:
            # Note: For batched inference (batch_size > 1), intermediate steps are not supported
            # This follows the same limitation as InferenceLDM
            if batch_pc.shape[0] > 1:
                print("⚠️  Warning: Batched inference with intermediate steps not supported. Skipping intermediate results.")
            else:
                for step_grasp in all_diffusion_grasps:
                    # Each step_grasp is (tmrp, cls_logit) or (tmrp, cls_logit, qualities)
                    step_tmrp = step_grasp[0]
                    # Reshape to ensure num_grasps dimension is consistently present
                    reshaped_step_tmrp = step_tmrp.view(1, num_grasps, -1)
                    step_grasp_unnorm = unnormalize_grasps(reshaped_step_tmrp, metas)
                    step_H_grasps = tmrp_to_H(step_grasp_unnorm)
                    all_steps_grasps.append(step_H_grasps.squeeze(0))  # Remove batch dimension

        # Compute confidence scores
        confidence = cls_logit.view(1, num_grasps, cls_logit.shape[-1])
        confidence = self._sigmoid(confidence)

        # Unnormalize point cloud
        pc_unnorm = unnormalize_pc(batch_pc, metas)

        return {
            "grasps": H_grasps.squeeze(0),      # [num_grasps, 4, 4]
            "grasp_tmrp": grasp_unnorm.squeeze(0),  # [num_grasps, 6] translation + rotation MRP
            "confidence": confidence.squeeze(0), # [num_grasps, 1]
            "pc": pc_unnorm.squeeze(0),         # [num_points, 3]
            "qualities": qualities,              # Optional quality metrics
            "all_steps_grasps": all_steps_grasps, # List of intermediate diffusion steps
        }

    def infer_from_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 20,
        return_intermediate: bool = False,
        visualize: bool = False,
        return_scene: bool = False,
        save_path: str = None
    ) -> Union[Dict[str, torch.Tensor], Any]:
        """
        Main inference method for generating grasps from raw point cloud data.

        This is the primary interface for the simplified inference pipeline.
        It accepts raw point cloud data and optional camera parameters, then
        performs the complete preprocessing and inference pipeline.

        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 20)
            return_intermediate: Whether to return intermediate diffusion steps (default: False)
            visualize: Whether to display 3D visualization (default: False)
            return_scene: If True and visualize=True, return scene object instead of showing
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            dict or Scene: If visualize=False, returns dict with:
                          - grasps: [num_grasps, 4, 4] transformation matrices
                          - grasp_tmrp: [num_grasps, 6] translation + rotation MRP
                          - confidence: [num_grasps, 1] success probabilities
                          - pc: [num_points, 3] processed point cloud
                          - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
                          If visualize=True and return_scene=True, returns 3D scene object
                          If visualize=True and return_scene=False, shows visualization and returns dict

        Example:
            >>> # Basic usage
            >>> results = inference.infer_from_pointcloud(pc_array, num_grasps=10)
            >>> grasps = results["grasps"]  # [10, 4, 4] transformation matrices
            >>> confidence = results["confidence"]  # [10, 1] success probabilities

            >>> # With intermediate steps for analysis
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=20,
            ...     return_intermediate=True
            ... )
            >>> intermediate_steps = results["all_steps_grasps"]  # List of intermediate results

            >>> # With camera pose transformation and saving
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_camera_coords,
            ...     camera_pose=camera_to_world_matrix,
            ...     num_grasps=20,
            ...     visualize=True,
            ...     save_path="grasps.glb"  # or .ply, .obj, .stl
            ... )
        """
        print(f"🚀 Starting grasp inference...")
        print(f"  Input PC shape: {pointcloud.shape}")
        print(f"  Target grasps: {num_grasps}")
        print(f"  Camera pose: {'Yes' if camera_pose is not None else 'No'}")
        print(f"  Return intermediate: {'Yes' if return_intermediate else 'No'}")

        # Step 1: Prepare point cloud
        pc_prepared = self._prepare_pointcloud(pointcloud, camera_pose)
        print(f"  ✓ Point cloud prepared: {pc_prepared.shape}")

        # Step 2: Preprocess (center + normalize)
        pc_preprocessed, metas = self._preprocess_pointcloud(pc_prepared)
        print(f"  ✓ Preprocessing complete")

        # Step 3: Generate grasps with intermediate step support
        results = self._generate_grasps(pc_preprocessed, metas, num_grasps, return_intermediate)
        print(f"  ✓ Generated {results['grasps'].shape[0]} grasps")
        if return_intermediate and results.get('all_steps_grasps'):
            print(f"  ✓ Captured {len(results['all_steps_grasps'])} intermediate steps")

        # Step 4: Visualization (if requested)
        if visualize:
            scene = self._visualize_results(results, return_scene, save_path)
            if return_scene:
                return scene
            # If not returning scene, show it and continue to return results

        print(f"✅ Inference complete!")
        return results

    def _visualize_results(self, results: Dict[str, torch.Tensor], return_scene: bool = False, save_path: str = None):
        """
        Visualize generated grasps in 3D with optional saving.

        Args:
            results: Results dictionary from inference
            return_scene: Whether to return scene object or show directly
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            Scene object if return_scene=True, otherwise None
        """
        # Convert to numpy for visualization
        pc_np = results["pc"].detach().cpu().numpy()
        grasps_np = results["grasps"].detach().cpu().numpy()
        confidence_np = results["confidence"].detach().cpu().numpy()

        # Create 3D scene
        scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)

        # Save scene if path provided
        if save_path is not None:
            try:
                scene.export(save_path)
                print(f"✅ Visualization saved to: {save_path}")
            except Exception as e:
                print(f"❌ Failed to save visualization: {e}")

        if return_scene:
            return scene
        else:
            scene.show(line_settings={"point_size": 10})
            return None

    def load_pointcloud_from_file(self, filepath: str) -> np.ndarray:
        """
        Load point cloud from common file formats.

        Supports .ply, .pcd, .xyz, and .txt files.

        Args:
            filepath: Path to point cloud file

        Returns:
            np.ndarray: Point cloud [N, 3]
        """
        import trimesh

        if filepath.endswith('.ply'):
            mesh = trimesh.load(filepath)
            if hasattr(mesh, 'vertices'):
                return np.array(mesh.vertices)
            else:
                raise ValueError("PLY file does not contain vertices")
        elif filepath.endswith('.pcd'):
            # Basic PCD file support
            import open3d as o3d
            pcd = o3d.io.read_point_cloud(filepath)
            return np.asarray(pcd.points)
        elif filepath.endswith(('.xyz', '.txt')):
            # Simple text format: x y z per line
            return np.loadtxt(filepath)[:, :3]
        else:
            raise ValueError(f"Unsupported file format: {filepath}")

    def filter_grasps_by_confidence(
        self,
        results: Dict[str, torch.Tensor],
        min_confidence: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        Filter generated grasps by confidence threshold.

        Args:
            results: Results dictionary from inference
            min_confidence: Minimum confidence threshold (0.0 to 1.0)

        Returns:
            dict: Filtered results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        mask = confidence >= min_confidence

        if mask.sum() == 0:
            print(f"⚠️  No grasps above confidence threshold {min_confidence:.2f}")
            return results

        filtered_results = {
            "grasps": results["grasps"][mask],
            "grasp_tmrp": results["grasp_tmrp"][mask],
            "confidence": results["confidence"][mask],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            filtered_results["qualities"] = results["qualities"][mask]

        print(f"✓ Filtered {mask.sum().item()}/{len(mask)} grasps above confidence {min_confidence:.2f}")
        return filtered_results

    def get_best_grasps(
        self,
        results: Dict[str, torch.Tensor],
        top_k: int = 5
    ) -> Dict[str, torch.Tensor]:
        """
        Get top-k grasps by confidence score.

        Args:
            results: Results dictionary from inference
            top_k: Number of top grasps to return

        Returns:
            dict: Top-k results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        _, top_indices = torch.topk(confidence, min(top_k, len(confidence)))

        top_results = {
            "grasps": results["grasps"][top_indices],
            "grasp_tmrp": results["grasp_tmrp"][top_indices],
            "confidence": results["confidence"][top_indices],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            top_results["qualities"] = results["qualities"][top_indices]

        print(f"✓ Selected top-{len(top_indices)} grasps")
        return top_results

    def save_visualization(self, results: Dict[str, torch.Tensor], save_path: str, 
                         include_confidence_colors: bool = True) -> bool:
        """
        Save visualization of grasps to file with enhanced options.
        
        Args:
            results: Results dictionary from inference
            save_path: Path to save the visualization
            include_confidence_colors: Whether to color grasps by confidence
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Convert to numpy for visualization
            pc_np = results["pc"].detach().cpu().numpy()
            grasps_np = results["grasps"].detach().cpu().numpy()
            confidence_np = results["confidence"].detach().cpu().numpy() if include_confidence_colors else None

            # Create 3D scene
            scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)
            
            # Ensure directory exists
            import os
            dir_path = os.path.dirname(save_path)
            if dir_path:  # Only create directory if path contains a directory
                os.makedirs(dir_path, exist_ok=True)
            
            # Export scene
            scene.export(save_path)
            
            # Validate file was created
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Visualization saved to: {save_path} ({file_size} bytes)")
                return True
            else:
                print(f"❌ File was not created: {save_path}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to save visualization: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_diffusion_animation(
        self,
        results: Dict[str, torch.Tensor],
        save_path: str = "diffusion_animation.gif",
        resolution: Tuple[int, int] = (800, 600),
        fps: int = 5,
        show_confidence_colors: bool = True,
        camera_distance: float = 0.8,
        show_progress: bool = True
    ) -> bool:
        """
        Create an animated visualization of the diffusion process evolution.
        
        This method generates a GIF animation showing how grasps evolve from noise
        to final poses during the diffusion denoising process. Each frame represents
        one denoising step, providing insight into the model's generation process.
        
        Args:
            results: Results dictionary from inference containing 'all_steps_grasps'
            save_path: Path to save the animation (supports .gif, .mp4)
            resolution: Output resolution as (width, height) tuple
            fps: Frames per second for the animation
            show_confidence_colors: Whether to color grasps by confidence (final step only)
            camera_distance: Camera distance from the scene center
            show_progress: Whether to display progress information
            
        Returns:
            bool: True if animation was created successfully, False otherwise
            
        Example:
            >>> # Generate grasps with intermediate steps
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=10,
            ...     return_intermediate=True
            ... )
            >>> 
            >>> # Create animation
            >>> success = inference.create_diffusion_animation(
            ...     results=results,
            ...     save_path="diffusion_evolution.gif",
            ...     fps=3,
            ...     resolution=(1024, 768)
            ... )
        """
        # Check if intermediate steps are available
        if not results.get('all_steps_grasps') or len(results['all_steps_grasps']) == 0:
            print("❌ No intermediate steps found in results. Please run inference with return_intermediate=True")
            return False
        
        try:
            import trimesh
            from PIL import Image
            import numpy as np
            
            # Try to import imageio for better GIF/MP4 support
            try:
                import imageio
                use_imageio = True
            except ImportError:
                print("⚠️  imageio not found, using PIL for GIF creation (MP4 not supported)")
                use_imageio = False
                if save_path.endswith('.mp4'):
                    print("❌ MP4 output requires imageio. Changing to GIF format.")
                    save_path = save_path.replace('.mp4', '.gif')
            
            print(f"🎬 Creating diffusion animation...")
            print(f"  Steps to animate: {len(results['all_steps_grasps'])}")
            print(f"  Output resolution: {resolution}")
            print(f"  Frame rate: {fps} FPS")
            print(f"  Save path: {save_path}")
            
            # Prepare point cloud data
            pc_np = results["pc"].detach().cpu().numpy()
            final_confidence = results["confidence"].detach().cpu().numpy() if show_confidence_colors else None
            
            # Create frames for each diffusion step
            frames = []
            all_steps = results['all_steps_grasps']
            total_steps = len(all_steps)
            
            for step_idx, step_grasps in enumerate(all_steps):
                if show_progress:
                    print(f"  Rendering frame {step_idx + 1}/{total_steps}...", end='\r')
                
                # Convert to numpy
                step_grasps_np = step_grasps.detach().cpu().numpy()
                
                # Use confidence colors only for the final step
                confidence_colors = final_confidence if (step_idx == total_steps - 1 and show_confidence_colors) else None
                
                # Create scene for this step
                scene = visualize_pc_grasps(pc_np, step_grasps_np, confidence_colors)
                
                # Set up camera
                bounds = scene.bounds
                center = bounds.mean(axis=0)
                scale = np.max(bounds[1] - bounds[0])
                
                # Configure camera position for consistent viewpoint
                camera_transform = np.eye(4)
                camera_transform[:3, 3] = center + np.array([0, 0, scale * camera_distance])
                
                # Render frame
                try:
                    # Use offscreen rendering
                    png_data = scene.save_image(
                        resolution=resolution,
                        camera_transform=camera_transform,
                        scene_lights='default'
                    )
                    
                    # Convert to PIL Image
                    if isinstance(png_data, bytes):
                        from io import BytesIO
                        frame = Image.open(BytesIO(png_data))
                    else:
                        frame = Image.fromarray(png_data)
                    
                    frames.append(frame)
                    
                except Exception as e:
                    print(f"\n⚠️  Warning: Failed to render frame {step_idx + 1}: {e}")
                    print("    Falling back to basic rendering...")
                    
                    # Fallback: create a simple placeholder frame
                    placeholder = Image.new('RGB', resolution, color=(50, 50, 50))
                    frames.append(placeholder)
            
            if show_progress:
                print(f"  Rendered {len(frames)} frames successfully")
            
            # Save animation
            if len(frames) == 0:
                print("❌ No frames were rendered successfully")
                return False
            
            print(f"  💾 Saving animation...")
            
            # Calculate frame duration in milliseconds
            frame_duration = int(1000 / fps)
            
            if use_imageio and save_path.endswith('.mp4'):
                # Save as MP4 using imageio
                with imageio.get_writer(save_path, fps=fps, codec='libx264') as writer:
                    for frame in frames:
                        writer.append_data(np.array(frame))
            else:
                # Save as GIF using PIL or imageio
                if use_imageio:
                    imageio.mimsave(save_path, [np.array(frame) for frame in frames], 
                                  duration=frame_duration/1000.0, loop=0)
                else:
                    frames[0].save(
                        save_path,
                        save_all=True,
                        append_images=frames[1:],
                        duration=frame_duration,
                        loop=0,
                        optimize=True
                    )
            
            # Validate output file
            import os
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Animation saved successfully!")
                print(f"  File: {save_path}")
                print(f"  Size: {file_size / (1024*1024):.2f} MB")
                print(f"  Frames: {len(frames)}")
                print(f"  Duration: {len(frames) / fps:.1f} seconds")
                return True
            else:
                print(f"❌ Failed to create animation file: {save_path}")
                return False
                
        except ImportError as e:
            print(f"❌ Missing required dependency for animation: {e}")
            print("Please install required packages: pip install trimesh pillow imageio imageio-ffmpeg")
            return False
        except Exception as e:
            print(f"❌ Failed to create animation: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_and_animate(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 10,
        animation_path: str = "diffusion_animation.gif",
        resolution: Tuple[int, int] = (800, 600),
        fps: int = 5,
        **kwargs
    ) -> Tuple[Dict[str, torch.Tensor], bool]:
        """
        Convenience method to generate grasps and create diffusion animation in one call.
        
        This method combines inference with intermediate steps and animation generation
        for a streamlined workflow when analyzing the diffusion process.
        
        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 10)
            animation_path: Path to save the animation file (default: "diffusion_animation.gif")
            resolution: Output resolution as (width, height) tuple (default: (800, 600))
            fps: Frames per second for the animation (default: 5)
            **kwargs: Additional arguments passed to infer_from_pointcloud
            
        Returns:
            tuple: (results_dict, animation_success) where:
                - results_dict: Complete inference results with intermediate steps
                - animation_success: Boolean indicating if animation was created successfully
                
        Example:
            >>> # Generate grasps and animation in one call
            >>> results, animation_ok = inference.generate_and_animate(
            ...     pointcloud=pc_array,
            ...     num_grasps=15,
            ...     animation_path="my_diffusion.gif",
            ...     fps=4
            ... )
            >>> 
            >>> if animation_ok:
            ...     print(f"Animation saved! Results contain {len(results['all_steps_grasps'])} steps")
        """
        print(f"🚀 Starting combined inference and animation generation...")
        
        # Step 1: Generate grasps with intermediate steps
        results = self.infer_from_pointcloud(
            pointcloud=pointcloud,
            camera_pose=camera_pose,
            num_grasps=num_grasps,
            return_intermediate=True,  # Force intermediate steps for animation
            visualize=False,  # Skip visualization to avoid conflicts
            **kwargs
        )
        
        # Step 2: Create animation from intermediate steps
        if results.get('all_steps_grasps'):
            print(f"✓ Generated {len(results['all_steps_grasps'])} intermediate steps")
            animation_success = self.create_diffusion_animation(
                results=results,
                save_path=animation_path,
                resolution=resolution,
                fps=fps
            )
        else:
            print("❌ No intermediate steps available for animation")
            animation_success = False
        
        print(f"✅ Combined generation complete!")
        print(f"  Final grasps: {results['grasps'].shape[0]}")
        print(f"  Animation: {'✅ Success' if animation_success else '❌ Failed'}")
        
        return results, animation_success


def demo_simple_inference():
    """
    Demonstration of the simplified inference interface.

    This function shows how to use the SimpleGraspLDMInference class
    with synthetic data to generate grasps.
    """
    print("🎯 GraspLDM Simplified Inference Demo")
    print("=" * 50)

    # Initialize inference engine
    exp_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"

    if not os.path.exists(exp_path):
        print(f"❌ Experiment path not found: {exp_path}")
        print("Please ensure you have downloaded the model checkpoints.")
        return

    try:
        inference = SimpleGraspLDMInference(
            exp_path=exp_path,
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            num_inference_steps=100  # Faster for demo
        )

        # Generate synthetic point cloud (sphere)
        print("\n📊 Generating synthetic point cloud...")
        theta = np.random.uniform(0, 2*np.pi, 2000)
        phi = np.random.uniform(0, np.pi, 2000)
        r = 0.18  # 18cm radius sphere

        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)

        synthetic_pc = np.column_stack([x, y, z])
        print(f"✓ Created sphere point cloud: {synthetic_pc.shape}")

        # Generate grasps
        print("\n🤖 Generating grasps...")
        results = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=False  # Set to True to see 3D visualization
        )

        # Demonstrate intermediate steps feature
        print("\n🔍 Generating grasps with intermediate steps...")
        results_with_steps = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=10,
            return_intermediate=True,
            visualize=False
        )

        # Demonstrate saving visualization
        print("\n💾 Saving visualization...")
        results_with_viz = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=True,
            save_path="demo_grasps.glb"  # Save as GLB format
        )

        # Analyze results
        print(f"\n📈 Results Analysis:")
        print(f"  Generated grasps: {results['grasps'].shape[0]}")
        print(f"  Confidence range: {results['confidence'].min():.3f} - {results['confidence'].max():.3f}")
        print(f"  Mean confidence: {results['confidence'].mean():.3f}")
        
        # Analyze intermediate steps
        if results_with_steps.get('all_steps_grasps'):
            print(f"  Intermediate steps captured: {len(results_with_steps['all_steps_grasps'])}")
            print(f"  Each step shape: {results_with_steps['all_steps_grasps'][0].shape}")
            
            # Create diffusion animation
            print("\n🎬 Creating diffusion animation...")
            animation_success = inference.create_diffusion_animation(
                results=results_with_steps,
                save_path="demo_diffusion_evolution.gif",
                resolution=(800, 600),
                fps=3,
                show_confidence_colors=True
            )
            
            if animation_success:
                print("  ✅ Animation created successfully!")
            else:
                print("  ❌ Animation creation failed")
        else:
            print(f"  No intermediate steps captured (may require return_intermediate=True)")

        # Filter high-confidence grasps
        good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.7)

        # Get top grasps
        top_grasps = inference.get_best_grasps(results, top_k=5)

        print(f"\n✅ Demo completed successfully!")
        print(f"  High-confidence grasps: {good_grasps['grasps'].shape[0]}")
        print(f"  Top-5 grasps confidence: {top_grasps['confidence'].squeeze().tolist()}")

        # Demonstrate the convenience method for combined generation and animation
        print(f"\n🎬 Demonstrating combined generation and animation...")
        try:
            combined_results, combined_success = inference.generate_and_animate(
                pointcloud=synthetic_pc,
                num_grasps=8,
                animation_path="demo_combined_diffusion.gif",
                resolution=(640, 480),
                fps=4
            )
            
            if combined_success:
                print(f"  ✅ Combined demo successful!")
                print(f"     Generated: {combined_results['grasps'].shape[0]} grasps")
                print(f"     Animation: demo_combined_diffusion.gif")
            else:
                print(f"  ⚠️  Combined demo completed with animation issues")
                
        except Exception as e:
            print(f"  ⚠️  Combined demo skipped due to: {e}")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_simple_inference()
